<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="time_range" label="时间">
                  <a-range-picker v-model="formModel.time_range" style="width: 100%" @change="handleTimeChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="phone" label="会员手机号">
                  <a-input v-model="formModel.phone" placeholder="请输入" @press-enter="search" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 33px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-table
        :loading="isLoading"
        :pagination="pagination"
        :data="renderData"
        :bordered="false"
        :size="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange">
        <template #columns>
          <a-table-column title="会员姓名" data-index="username" align="center" />
          <a-table-column title="会员手机号" data-index="member_phone" align="center" />
          <a-table-column title="协议名称" data-index="protocol_name" align="center">
            <template #cell="{ record }">
              <a-link @click="handleViewProtocol(record)">{{ record.protocol_name }}</a-link>
            </template>
          </a-table-column>
          <a-table-column title="签署时间" data-index="sign_time" align="center" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import type { CalendarValue } from '@arco-design/web-vue/es/date-picker/interface';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import dayjs from 'dayjs';
  import Breadcrumb from '@/components/breadcrumb/index.vue';
  import { Pagination } from '@/types/global';
  import { getMemberProtocolList, MemberProtocolType } from '@/api/member-protocol';

  defineOptions({
    name: 'MemberProtocol',
  });

  const generateFormModel = () => {
    return {
      time_range: [] as CalendarValue[],
      phone: '',
      begin_time: '',
      end_time: '',
    };
  };

  const { isLoading, execute: fetchList } = getMemberProtocolList();
  const renderData = ref<MemberProtocolType[]>([]);
  const formModel = ref(generateFormModel());
  const basePagination: Pagination = {
    current: 1,
    pageSize: 10,
  };
  const size = ref<'medium'>('medium');
  const pagination = reactive({
    ...basePagination,
    showPageSize: true,
    showTotal: true,
  });

  const fetchData = async () => {
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      phone: formModel.value.phone,
      begin_time: formModel.value.begin_time,
      end_time: formModel.value.end_time,
    };

    const { data } = await fetchList({ data: params });
    if (data.value) {
      renderData.value = data.value.list || [];
      pagination.total = Number(data.value.count || 0);
    }
  };

  const handleTimeChange = (value: (CalendarValue | undefined)[] | undefined) => {
    if (value && value.length === 2 && value[0] && value[1]) {
      // Convert CalendarValue to dayjs for formatting
      const startDate = dayjs(value[0] as any);
      const endDate = dayjs(value[1] as any);
      formModel.value.begin_time = startDate.format('YYYY-MM-DD');
      formModel.value.end_time = endDate.format('YYYY-MM-DD');
    } else {
      formModel.value.begin_time = '';
      formModel.value.end_time = '';
    }
  };

  const search = async () => {
    pagination.current = 1;
    await fetchData();
  };

  const onPageChange = (current: number) => {
    pagination.current = current;
    fetchData();
  };

  const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.current = 1;
    fetchData();
  };

  const handleViewProtocol = (record: MemberProtocolType) => {
    // Handle protocol view logic here
    console.log('View protocol:', record);
  };

  // Initialize data
  onMounted(() => {
    fetchData();
  });
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }

  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
</style>
