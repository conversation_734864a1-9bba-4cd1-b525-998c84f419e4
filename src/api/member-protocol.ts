import request from '@/request/index';

export interface MemberProtocolType {
  // 会员姓名
  member_name: string;
  // 会员手机号
  member_phone: string;
  // 协议名称
  protocol_name: string;
  // 签署时间
  sign_time: string;
  // 协议ID
  protocol_id: string;
  // 会员ID
  member_id: string;
}

export interface MemberProtocolListData {
  // 当前分页条数，不传默认10
  limit?: number;
  // 当前查询页数，不传默认1
  page?: number;
  // 会员手机号
  phone?: string;
  // 开始时间
  begin_time?: string;
  // 结束时间
  end_time?: string;
}

export interface MemberProtocolListRes {
  // 总条数
  count: number;
  // 数据列表
  list: MemberProtocolType[];
  // 最大页数
  max_page: number;
  // 当前页
  page: number;
}

// 获取会员协议列表
export function getMemberProtocolList() {
  return request<MemberProtocolListRes>({
    url: '/Web/AgreementAcceptLog/getAgreementList',
    options: {
      method: 'get',
    },
    immediate: false,
  });
}

// 获取协议详情
export function getProtocolDetail(data: { protocol_id: string }) {
  return request({
    url: '/Web/AgreementAcceptLog/getDetail',
    options: {
      method: 'post',
      data,
    },
  });
}
